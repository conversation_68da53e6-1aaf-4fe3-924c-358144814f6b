/**
 * PangeaFlow - Next-generation agentic AI workflow system
 * 
 * A reactive, event-driven framework for building scalable AI agent workflows
 * with built-in observability, error recovery, and dynamic orchestration.
 * 
 * Key improvements over traditional approaches:
 * - Event-driven architecture for loose coupling
 * - Reactive state management with automatic invalidation
 * - Built-in telemetry and observability
 * - Dynamic workflow adaptation based on runtime conditions
 * - Memory-efficient streaming for large datasets
 * - Hierarchical error boundaries with recovery strategies
 */

// ============================================================================
// CORE TYPES & INTERFACES
// ============================================================================

/**
 * Unique identifier for workflow components.
 * Uses branded typing to ensure type safety and prevent mixing with regular strings.
 * @example
 * ```typescript
 * const id: ComponentId = "reasoning-agent" as ComponentId;
 * ```
 */
type ComponentId = string & { readonly __brand: unique symbol };

/**
 * Event types for workflow communication.
 * Represents events that flow through the workflow system for inter-component communication.
 * @template T - The type of the event payload
 * @example
 * ```typescript
 * const event: WorkflowEvent<string> = {
 *   id: "evt-123",
 *   type: "reasoning.completed",
 *   timestamp: Date.now(),
 *   source: "reasoning-agent" as ComponentId,
 *   payload: "Analysis complete",
 *   correlationId: "workflow-456"
 * };
 * ```
 */
interface WorkflowEvent<T = unknown> {
  /** Unique identifier for this event */
  readonly id: string;
  /** Event type identifier for routing and filtering */
  readonly type: string;
  /** Unix timestamp when the event was created */
  readonly timestamp: number;
  /** Component that emitted this event */
  readonly source: ComponentId;
  /** Event data payload */
  readonly payload: T;
  /** Optional correlation ID for tracking related events */
  readonly correlationId?: string;
}

/**
 * Context passed through workflow execution.
 * Contains all the state and metadata needed for component execution.
 * @example
 * ```typescript
 * const context: ExecutionContext = {
 *   id: "exec-123",
 *   startTime: Date.now(),
 *   sharedState: { userInput: "Hello world" },
 *   trace: ["start", "reasoning-agent"],
 *   nodeOutputs: new Map([["reasoning-agent", "Analysis result"]]),
 *   events: []
 * };
 * ```
 */
interface ExecutionContext {
  /** Unique identifier for this execution */
  readonly id: string;
  /** Unix timestamp when execution started */
  readonly startTime: number;
  /** Shared state accessible by all components */
  readonly sharedState: Record<string, unknown>;
  /** Execution trace showing component execution order */
  readonly trace: string[];
  /** Outputs from each executed component */
  readonly nodeOutputs: Map<string, unknown>;
  /** Events generated during execution */
  readonly events: WorkflowEvent[];
}

/**
 * Result of component execution.
 * Contains the outcome, output data, and instructions for workflow continuation.
 * @template T - The type of the output data
 * @example
 * ```typescript
 * const result: ExecutionResult<string> = {
 *   success: true,
 *   output: "Processing complete",
 *   events: [],
 *   nextActions: ["continue", "notify"],
 *   sharedStateUpdates: { status: "completed" }
 * };
 * ```
 */
interface ExecutionResult<T = unknown> {
  /** Whether the execution was successful */
  readonly success: boolean;
  /** Output data from the execution */
  readonly output?: T;
  /** Error information if execution failed */
  readonly error?: Error;
  /** Events generated during execution */
  readonly events: WorkflowEvent[];
  /** Actions to execute next in the workflow */
  readonly nextActions: string[];
  /** Updates to merge into shared state */
  readonly sharedStateUpdates: Record<string, unknown>;
}

/**
 * Telemetry data for monitoring and observability.
 * Captures performance metrics and execution metadata for analysis.
 * @example
 * ```typescript
 * const telemetry: TelemetryData = {
 *   componentId: "reasoning-agent" as ComponentId,
 *   operation: "execute",
 *   duration: 1250,
 *   success: true,
 *   metadata: { promptLength: 500, responseLength: 200 }
 * };
 * ```
 */
interface TelemetryData {
  /** Component that generated this telemetry */
  readonly componentId: ComponentId;
  /** Operation being measured */
  readonly operation: string;
  /** Duration in milliseconds */
  readonly duration: number;
  /** Whether the operation was successful */
  readonly success: boolean;
  /** Additional metadata about the operation */
  readonly metadata: Record<string, unknown>;
}

// ============================================================================
// EVENT SYSTEM
// ============================================================================

/**
 * Event bus for workflow communication.
 * Provides a publish-subscribe mechanism for components to communicate through events.
 * @example
 * ```typescript
 * const eventBus = new EventBus();
 *
 * // Subscribe to events
 * const unsubscribe = eventBus.on('reasoning.completed', (event) => {
 *   console.log('Reasoning completed:', event.payload);
 * });
 *
 * // Emit an event
 * eventBus.emit('reasoning.completed', 'Analysis done', 'reasoning-agent' as ComponentId);
 *
 * // Cleanup
 * unsubscribe();
 * ```
 */
class EventBus {
  private readonly listeners = new Map<string, Set<(event: WorkflowEvent) => void>>();

  /**
   * Emits an event to all registered listeners.
   * @template T - The type of the event payload
   * @param type - Event type identifier
   * @param payload - Event data
   * @param source - Component that is emitting the event
   * @param correlationId - Optional correlation ID for tracking related events
   * @example
   * ```typescript
   * eventBus.emit('tool.executed', { result: 'success' }, 'tool-agent' as ComponentId, 'workflow-123');
   * ```
   */
  emit<T>(type: string, payload: T, source: ComponentId, correlationId?: string): void {
    const event: WorkflowEvent<T> = {
      id: crypto.randomUUID(),
      type,
      timestamp: Date.now(),
      source,
      payload,
      correlationId,
    };

    this.listeners.get(type)?.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`Event listener error for ${type}:`, error);
      }
    });
  }

  /**
   * Registers a listener for events of the specified type.
   * @param type - Event type to listen for
   * @param listener - Function to call when event is emitted
   * @returns Unsubscribe function to remove the listener
   * @example
   * ```typescript
   * const unsubscribe = eventBus.on('error', (event) => {
   *   console.error('Workflow error:', event.payload);
   * });
   *
   * // Later, remove the listener
   * unsubscribe();
   * ```
   */
  on(type: string, listener: (event: WorkflowEvent) => void): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(listener);

    return () => this.listeners.get(type)?.delete(listener);
  }

  /**
   * Registers a one-time listener for events of the specified type.
   * The listener will be automatically removed after the first event.
   * @param type - Event type to listen for
   * @param listener - Function to call when event is emitted
   * @returns Unsubscribe function to remove the listener before it fires
   * @example
   * ```typescript
   * eventBus.once('workflow.completed', (event) => {
   *   console.log('Workflow finished:', event.payload);
   * });
   * ```
   */
  once(type: string, listener: (event: WorkflowEvent) => void): () => void {
    const wrapper = (event: WorkflowEvent) => {
      listener(event);
      this.listeners.get(type)?.delete(wrapper);
    };
    return this.on(type, wrapper);
  }
}

// ============================================================================
// REACTIVE STATE MANAGEMENT
// ============================================================================

/**
 * Reactive state management with automatic invalidation and computed values.
 * Provides observable state with subscription capabilities and cached computed properties.
 * @template T - The type of the state value
 * @example
 * ```typescript
 * const state = new ReactiveState({ count: 0, name: 'test' });
 *
 * // Subscribe to changes
 * const unsubscribe = state.subscribe((newValue, oldValue) => {
 *   console.log('State changed:', newValue, 'from:', oldValue);
 * });
 *
 * // Update state
 * state.set({ count: 1, name: 'updated' });
 * state.set(prev => ({ ...prev, count: prev.count + 1 }));
 *
 * // Computed values with caching
 * const doubled = state.computed('doubled', val => val.count * 2);
 * ```
 */
class ReactiveState<T> {
  private value: T;
  private readonly subscribers = new Set<(value: T, previous: T) => void>();
  private readonly computedCache = new Map<string, unknown>();

  /**
   * Creates a new reactive state instance.
   * @param initialValue - The initial state value
   */
  constructor(initialValue: T) {
    this.value = initialValue;
  }

  /**
   * Gets the current state value.
   * @returns The current state value
   */
  get(): T {
    return this.value;
  }

  /**
   * Sets a new state value and notifies subscribers if the value changed.
   * @param newValue - New value or updater function
   * @example
   * ```typescript
   * // Direct value
   * state.set({ count: 5 });
   *
   * // Updater function
   * state.set(prev => ({ ...prev, count: prev.count + 1 }));
   * ```
   */
  set(newValue: T | ((prev: T) => T)): void {
    const previous = this.value;
    this.value = typeof newValue === 'function' ? (newValue as (prev: T) => T)(previous) : newValue;

    if (this.value !== previous) {
      this.computedCache.clear();
      this.subscribers.forEach(subscriber => {
        try {
          subscriber(this.value, previous);
        } catch (error) {
          console.error('State subscriber error:', error);
        }
      });
    }
  }

  /**
   * Subscribes to state changes.
   * @param callback - Function to call when state changes
   * @returns Unsubscribe function
   * @example
   * ```typescript
   * const unsubscribe = state.subscribe((newVal, oldVal) => {
   *   console.log('Changed from', oldVal, 'to', newVal);
   * });
   *
   * // Later, cleanup
   * unsubscribe();
   * ```
   */
  subscribe(callback: (value: T, previous: T) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  /**
   * Creates a computed value with caching.
   * The computation is cached until the state changes.
   * @template R - The type of the computed result
   * @param key - Unique key for this computed value
   * @param fn - Function to compute the value
   * @returns The computed result
   * @example
   * ```typescript
   * const doubled = state.computed('doubled', val => val.count * 2);
   * const formatted = state.computed('formatted', val => `Count: ${val.count}`);
   * ```
   */
  computed<R>(key: string, fn: (value: T) => R): R {
    if (!this.computedCache.has(key)) {
      this.computedCache.set(key, fn(this.value));
    }
    return this.computedCache.get(key) as R;
  }
}

// ============================================================================
// TELEMETRY & OBSERVABILITY
// ============================================================================

/**
 * Telemetry collector for monitoring and observability.
 * Collects performance metrics and provides statistical analysis of component execution.
 * @example
 * ```typescript
 * const telemetry = new TelemetryCollector();
 *
 * // Record metrics
 * telemetry.record({
 *   componentId: 'reasoning-agent' as ComponentId,
 *   operation: 'execute',
 *   duration: 1250,
 *   success: true,
 *   metadata: { promptLength: 500 }
 * });
 *
 * // Get statistics
 * const stats = telemetry.getStats('reasoning-agent' as ComponentId);
 * console.log(`Success rate: ${stats.successRate * 100}%`);
 * ```
 */
class TelemetryCollector {
  private readonly metrics: TelemetryData[] = [];
  private readonly maxMetrics = 10000;

  /**
   * Records a telemetry data point.
   * Automatically manages memory by keeping only the most recent metrics.
   * @param data - Telemetry data to record
   * @example
   * ```typescript
   * telemetry.record({
   *   componentId: 'tool-agent' as ComponentId,
   *   operation: 'file_read',
   *   duration: 45,
   *   success: true,
   *   metadata: { fileSize: 1024 }
   * });
   * ```
   */
  record(data: TelemetryData): void {
    this.metrics.push(data);
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.splice(0, this.metrics.length - this.maxMetrics);
    }
  }

  /**
   * Retrieves metrics with optional filtering.
   * @param filter - Optional filter criteria to match against metrics
   * @returns Array of matching telemetry data
   * @example
   * ```typescript
   * // Get all metrics
   * const allMetrics = telemetry.getMetrics();
   *
   * // Get metrics for specific component
   * const agentMetrics = telemetry.getMetrics({
   *   componentId: 'reasoning-agent' as ComponentId
   * });
   *
   * // Get failed operations
   * const failures = telemetry.getMetrics({ success: false });
   * ```
   */
  getMetrics(filter?: Partial<TelemetryData>): TelemetryData[] {
    if (!filter) return [...this.metrics];

    return this.metrics.filter(metric =>
      Object.entries(filter).every(([key, value]) =>
        metric[key as keyof TelemetryData] === value
      )
    );
  }

  /**
   * Calculates statistical summary of metrics.
   * @param componentId - Optional component ID to filter metrics
   * @returns Statistical summary including success rate, average duration, etc.
   * @example
   * ```typescript
   * // Overall statistics
   * const overallStats = telemetry.getStats();
   *
   * // Component-specific statistics
   * const agentStats = telemetry.getStats('reasoning-agent' as ComponentId);
   * console.log(`Average duration: ${agentStats.avgDuration}ms`);
   * console.log(`Success rate: ${agentStats.successRate * 100}%`);
   * ```
   */
  getStats(componentId?: ComponentId): {
    totalExecutions: number;
    successRate: number;
    avgDuration: number;
    errorRate: number;
  } {
    const relevant = componentId
      ? this.metrics.filter(m => m.componentId === componentId)
      : this.metrics;

    const total = relevant.length;
    const successful = relevant.filter(m => m.success).length;
    const totalDuration = relevant.reduce((sum, m) => sum + m.duration, 0);

    return {
      totalExecutions: total,
      successRate: total > 0 ? successful / total : 0,
      avgDuration: total > 0 ? totalDuration / total : 0,
      errorRate: total > 0 ? (total - successful) / total : 0,
    };
  }
}

// ============================================================================
// CORE AGENT COMPONENT
// ============================================================================

/**
 * Abstract base class for all agent components in the workflow system.
 * Provides common functionality including event emission, telemetry, and state management.
 * @example
 * ```typescript
 * class CustomAgent extends AgentComponent {
 *   async execute(context: ExecutionContext): Promise<ExecutionResult> {
 *     return this.withTelemetry('custom_operation', async () => {
 *       // Custom logic here
 *       this.emit('custom.completed', { result: 'success' });
 *
 *       return {
 *         success: true,
 *         output: 'Custom operation completed',
 *         events: [],
 *         nextActions: ['continue'],
 *         sharedStateUpdates: { customData: 'value' }
 *       };
 *     });
 *   }
 * }
 * ```
 */
abstract class AgentComponent {
  /** Unique identifier for this component */
  readonly id: ComponentId;
  /** Event bus for inter-component communication */
  protected readonly eventBus: EventBus;
  /** Telemetry collector for performance monitoring */
  protected readonly telemetry: TelemetryCollector;
  /** Reactive state management for component-specific data */
  protected readonly state: ReactiveState<Record<string, unknown>>;

  /**
   * Creates a new agent component.
   * @param id - Unique identifier for the component
   * @param eventBus - Event bus for communication
   * @param telemetry - Telemetry collector for monitoring
   * @param initialState - Initial state for the component
   */
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    initialState: Record<string, unknown> = {}
  ) {
    this.id = id as ComponentId;
    this.eventBus = eventBus;
    this.telemetry = telemetry;
    this.state = new ReactiveState(initialState);
  }

  /**
   * Executes the component's main logic.
   * Must be implemented by concrete agent classes.
   * @param context - Execution context containing shared state and metadata
   * @returns Promise resolving to execution result
   */
  abstract execute(context: ExecutionContext): Promise<ExecutionResult>;

  /**
   * Emits an event through the event bus.
   * @template T - Type of the event payload
   * @param type - Event type identifier
   * @param payload - Event data
   * @param correlationId - Optional correlation ID for tracking
   * @example
   * ```typescript
   * this.emit('processing.started', { step: 1 }, context.id);
   * ```
   */
  protected emit<T>(type: string, payload: T, correlationId?: string): void {
    this.eventBus.emit(type, payload, this.id, correlationId);
  }

  /**
   * Wraps an operation with automatic telemetry collection.
   * Measures execution time and records success/failure metrics.
   * @template T - Return type of the operation
   * @param operation - Name of the operation for telemetry
   * @param fn - Async function to execute
   * @returns Promise resolving to the operation result
   * @example
   * ```typescript
   * const result = await this.withTelemetry('data_processing', async () => {
   *   return await processData(input);
   * });
   * ```
   */
  protected async withTelemetry<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const start = performance.now();
    let success = true;
    let error: Error | undefined;

    try {
      return await fn();
    } catch (e) {
      success = false;
      error = e as Error;
      throw e;
    } finally {
      const duration = performance.now() - start;
      this.telemetry.record({
        componentId: this.id,
        operation,
        duration,
        success,
        metadata: { error: error?.message },
      });
    }
  }
}

// ============================================================================
// SPECIALIZED AGENT COMPONENTS
// ============================================================================

/**
 * LLM-powered reasoning component for intelligent decision making.
 * Processes context and generates reasoned responses with next action recommendations.
 * @example
 * ```typescript
 * const reasoningAgent = new ReasoningAgent(
 *   'reasoning-agent',
 *   eventBus,
 *   telemetry,
 *   async (prompt, context) => {
 *     // Your LLM integration here
 *     return await callLLM(prompt, context);
 *   }
 * );
 * ```
 */
class ReasoningAgent extends AgentComponent {
  /**
   * Creates a new reasoning agent.
   * @param id - Unique identifier for the agent
   * @param eventBus - Event bus for communication
   * @param telemetry - Telemetry collector for monitoring
   * @param llmProvider - Function that calls the LLM with prompt and context
   */
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    private readonly llmProvider: (prompt: string, context: Record<string, unknown>) => Promise<string>
  ) {
    super(id, eventBus, telemetry);
  }

  /**
   * Executes reasoning by building a prompt from context and calling the LLM.
   * @param context - Execution context with shared state and previous outputs
   * @returns Promise resolving to execution result with LLM response and parsed actions
   */
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('reasoning', async () => {
      const prompt = this.buildPrompt(context);
      const response = await this.llmProvider(prompt, context.sharedState);

      const actions = this.parseActions(response);

      this.emit('reasoning.completed', { response, actions }, context.id);

      return {
        success: true,
        output: response,
        events: [],
        nextActions: actions,
        sharedStateUpdates: { promptLength: prompt.length, responseLength: response.length },
      };
    });
  }

  /**
   * Builds a prompt from the execution context.
   * @param context - Execution context containing outputs and trace
   * @returns Formatted prompt string for the LLM
   * @private
   */
  private buildPrompt(context: ExecutionContext): string {
    const contextStr = Array.from(context.nodeOutputs.entries())
      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
      .join('\n');

    return `Context:\n${contextStr}\n\nTrace: ${context.trace.join(' -> ')}\n\nProvide your reasoning and next actions:`;
  }

  /**
   * Parses actions from the LLM response.
   * @param response - LLM response text
   * @returns Array of action strings to execute next
   * @private
   */
  private parseActions(response: string): string[] {
    // Simple action parsing - can be enhanced with structured output
    const actionMatch = response.match(/ACTIONS?:\s*(.+)/i);
    if (!actionMatch) return ['continue'];

    return actionMatch[1]
      .split(',')
      .map(action => action.trim().toLowerCase())
      .filter(Boolean);
  }
}

/**
 * Tool execution component for running external tools and functions.
 * Manages a registry of tools and executes them based on shared state configuration.
 * @example
 * ```typescript
 * const toolAgent = new ToolAgent(
 *   'tool-agent',
 *   eventBus,
 *   telemetry,
 *   {
 *     'file_read': async (args) => await fs.readFile(args.path, 'utf8'),
 *     'api_call': async (args) => await fetch(args.url).then(r => r.json()),
 *     'calculate': async (args) => args.a + args.b
 *   }
 * );
 * ```
 */
class ToolAgent extends AgentComponent {
  /**
   * Creates a new tool agent.
   * @param id - Unique identifier for the agent
   * @param eventBus - Event bus for communication
   * @param telemetry - Telemetry collector for monitoring
   * @param tools - Map of tool names to tool functions
   */
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    private readonly tools: Map<string, (args: unknown) => Promise<unknown>>
  ) {
    super(id, eventBus, telemetry);
  }

  /**
   * Executes a tool based on the shared state configuration.
   * Expects `tool` and `args` properties in the shared state.
   * @param context - Execution context containing tool name and arguments in shared state
   * @returns Promise resolving to execution result with tool output
   * @throws Error if the specified tool is not found
   */
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('tool_execution', async () => {
      const toolName = context.sharedState.tool as string;
      const toolArgs = context.sharedState.args;

      if (!toolName || !this.tools.has(toolName)) {
        throw new Error(`Tool '${toolName}' not found`);
      }

      const tool = this.tools.get(toolName)!;
      const result = await tool(toolArgs);

      this.emit('tool.executed', { toolName, result }, context.id);

      return {
        success: true,
        output: result,
        events: [],
        nextActions: ['continue'],
        sharedStateUpdates: { toolName, executedAt: Date.now() },
      };
    });
  }
}


/* =================================================================== *
 * 3.  Memory persistence plug-in layer                                *
 * =================================================================== */

/** Standard contract every persistence plug-in must implement. */
export interface MemoryDriver {
  /** Fetch a value (or undefined if missing / expired). */
  get<T = unknown>(key: string): Promise<T | undefined>;

  /** Persist a value. `ttlMs` is optional; driver decides how to honour it. */
  set<T = unknown>(key: string, value: T, ttlMs?: number): Promise<void>;

  /** Remove one key. */
  delete(key: string): Promise<void>;

  /** Remove everything in this namespace (implementation-defined). */
  clear(namespace?: string): Promise<void>;

  /** List keys (optionally filtered by namespace). */
  keys(namespace?: string): Promise<string[]>;

  /** Flush / close network resources if necessary. */
  close(): Promise<void>;
}
/* ------------------------------------------------------------------ *
 * Registry to install & discover drivers at runtime.                 *
 * ------------------------------------------------------------------ */
type DriverFactory = (options?: unknown) => MemoryDriver;

class MemoryDriverRegistry {
  private factories = new Map<string, DriverFactory>();

  /** Register a new driver (idempotent per `name`). */
  register(name: string, factory: DriverFactory): void {
    if (this.factories.has(name)) {
      throw new Error(`MemoryDriver "${name}" is already registered`);
    }
    this.factories.set(name, factory);
  }

  /** Instantiate a driver by name. */
  create(name: string, options?: unknown): MemoryDriver {
    const factory = this.factories.get(name);
    if (!factory) {
      throw new Error(
        `Unknown MemoryDriver "${name}". ` +
        `Did you forget to install its plug-in and call registerMemoryDriver()?`
      );
    }
    return factory(options);
  }

  /** List all registered driver names (mainly for CLI tooling). */
  list(): string[] {
    return [...this.factories.keys()];
  }
}

/** Global, shared registry instance – importable by plug-ins. */
export const memoryDrivers = new MemoryDriverRegistry();


/* ------------------------------------------------------------------ *
 * Built-in “mem” driver (single-process Map) to preserve behaviour.  *
 * This stays tiny; production-grade drivers ship as separate modules *
 * ------------------------------------------------------------------ */

class InMemoryDriver implements MemoryDriver {
  private readonly store = new Map<string, { value: unknown; expires?: number }>();

  async get<T>(key: string): Promise<T | undefined> {
    const entry = this.store.get(key);
    if (!entry) return undefined;
    if (entry.expires && Date.now() > entry.expires) {
      this.store.delete(key);
      return undefined;
    }
    return entry.value as T;
  }

  async set<T>(key: string, value: T, ttlMs?: number): Promise<void> {
    this.store.set(key, {
      value,
      expires: ttlMs ? Date.now() + ttlMs : undefined,
    });
  }

  async delete(key: string): Promise<void> {
    this.store.delete(key);
  }

  async clear(): Promise<void> {
    this.store.clear();
  }

  async keys(): Promise<string[]> {
    return [...this.store.keys()];
  }

  async close(): Promise<void> {
    /* nothing to clean up */
  }
}

/* Register the built-in driver under the id "mem". */
memoryDrivers.register('mem', () => new InMemoryDriver());


/**
 * Memory management component for storing and retrieving data.
 * Provides both short-term (session) and long-term (persistent) memory storage.
 * @example
 * ```typescript
 * const memoryAgent = new MemoryAgent('memory-agent', eventBus, telemetry);
 *
 * // Store data
 * await orchestrator.execute('memory', {
 *   sharedState: {
 *     operation: 'store',
 *     key: 'user_preference',
 *     value: { theme: 'dark', language: 'en' },
 *     persistent: true
 *   }
 * });
 *
 * // Retrieve data
 * await orchestrator.execute('memory', {
 *   sharedState: {
 *     operation: 'retrieve',
 *     key: 'user_preference',
 *     persistent: true
 *   }
 * });
 * ```
 */
class MemoryAgent extends AgentComponent {
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    private readonly driver: MemoryDriver = memoryDrivers.create('mem'),
    private readonly defaultTTL?: number
  ) {
    super(id, eventBus, telemetry);
  }

  async execute(ctx: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('memory_operation', async () => {
      const { operation, key, value, ttlMs } = ctx.sharedState as {
        operation: 'store' | 'retrieve' | 'delete' | 'clear';
        key?: string;
        value?: unknown;
        ttlMs?: number;
      };

      switch (operation) {
        case 'store':
          if (!key) throw new Error('memory.store requires "key"');
          await this.driver.set(key, value, ttlMs ?? this.defaultTTL);
          this.emit('memory.stored', { key }, ctx.id);
          break;

        case 'retrieve': {
          if (!key) throw new Error('memory.retrieve requires "key"');
          const out = await this.driver.get(key);
          this.emit('memory.retrieved', { key, hit: out !== undefined }, ctx.id);
          return {
            success: true,
            output: out,
            events: [],
            nextActions: ['continue'],
            sharedStateUpdates: { retrievedKey: key },
          };
        }

        case 'delete':
          if (!key) throw new Error('memory.delete requires "key"');
          await this.driver.delete(key);
          this.emit('memory.deleted', { key }, ctx.id);
          break;

        case 'clear':
          await this.driver.clear();
          this.emit('memory.cleared', {}, ctx.id);
          break;

        default:
          throw new Error(`Unknown memory operation "${operation}"`);
      }

      return {
        success: true,
        events: [],
        nextActions: ['continue'],
        sharedStateUpdates: { operation, key },
      };
    });
  }

  /** Ensure underlying connection is closed when the agent is disposed. */
  async close(): Promise<void> {
    await this.driver.close();
  }
}
export interface MemoryAgentOptions {
  /** Pass a *registered* driver id, or inject a concrete driver instance. */
  driver?: string | MemoryDriver;
  /** Options forwarded to the driver factory (when `driver` is a string). */
  driverOptions?: unknown;
  /** Default TTL (ms) for keys written by this agent (optional). */
  defaultTTL?: number;
}


// ============================================================================
// WORKFLOW ORCHESTRATOR
// ============================================================================

/**
 * Workflow orchestrator for managing and executing agent workflows.
 * Coordinates component execution, handles routing, and provides observability.
 * @example
 * ```typescript
 * const orchestrator = new WorkflowOrchestrator();
 *
 * // Register components
 * orchestrator
 *   .registerComponent(reasoningAgent)
 *   .registerComponent(toolAgent)
 *   .registerComponent(memoryAgent);
 *
 * // Define routing
 * orchestrator
 *   .defineRoute('start', ['reasoning-agent' as ComponentId])
 *   .defineRoute('continue', ['tool-agent' as ComponentId, 'memory-agent' as ComponentId]);
 *
 * // Execute workflow
 * const results = await orchestrator.execute('start', {
 *   sharedState: { userInput: 'Hello world' }
 * });
 * ```
 */
class WorkflowOrchestrator {
  /** Registry of all workflow components */
  private readonly components = new Map<ComponentId, AgentComponent>();
  /** Action routing configuration */
  private readonly routes = new Map<string, ComponentId[]>();
  /** Event bus for inter-component communication */
  private readonly eventBus = new EventBus();
  /** Telemetry collector for monitoring */
  private readonly telemetry = new TelemetryCollector();

  /**
   * Registers a component with the orchestrator.
   * @param component - Agent component to register
   * @returns This orchestrator instance for method chaining
   * @example
   * ```typescript
   * orchestrator.registerComponent(new ReasoningAgent('reasoning', eventBus, telemetry, llmProvider));
   * ```
   */
  registerComponent(component: AgentComponent): this {
    this.components.set(component.id, component);
    return this;
  }

  /**
   * Defines routing for an action to specific components.
   * @param action - Action name that triggers this route
   * @param componentIds - Array of component IDs to execute for this action
   * @returns This orchestrator instance for method chaining
   * @example
   * ```typescript
   * orchestrator.defineRoute('analyze', ['reasoning-agent' as ComponentId, 'tool-agent' as ComponentId]);
   * ```
   */
  defineRoute(action: string, componentIds: ComponentId[]): this {
    this.routes.set(action, componentIds);
    return this;
  }
  
  /**
   * Executes a workflow starting with the specified action.
   * Manages component execution, state propagation, and error handling.
   * @param startAction - Initial action to trigger the workflow
   * @param initialContext - Optional initial context to merge with default context
   * @returns Promise resolving to array of execution results from all components
   * @example
   * ```typescript
   * const results = await orchestrator.execute('analyze_code', {
   *   sharedState: {
   *     sourceCode: 'function hello() { return "world"; }',
   *     language: 'javascript'
   *   }
   * });
   *
   * console.log('Workflow completed with', results.length, 'steps');
   * ```
   */
  async execute(
    startAction: string,
    initialContext: Partial<ExecutionContext> = {}
  ): Promise<ExecutionResult[]> {
    const context: ExecutionContext = {
      id: crypto.randomUUID(),
      startTime: Date.now(),
      sharedState: {},
      trace: [],
      nodeOutputs: new Map(),
      events: [],
      ...initialContext,
    };
    
    const results: ExecutionResult[] = [];
    const pendingActions = [startAction];
    const executedComponents = new Set<ComponentId>();
    
    while (pendingActions.length > 0) {
      const currentAction = pendingActions.shift()!;
      const componentIds = this.routes.get(currentAction) || [];
      
      for (const componentId of componentIds) {
        // Prevent infinite loops
        if (executedComponents.has(componentId)) continue;
        executedComponents.add(componentId);
        
        const component = this.components.get(componentId);
        if (!component) {
          console.warn(`Component ${componentId} not found for action ${currentAction}`);
          continue;
        }
        
        try {
          context.trace.push(componentId);
          const result = await component.execute(context);
          results.push(result);

          // Check if the result indicates failure
          if (!result.success && result.error) {
            // Emit error event for failed agent execution
            this.eventBus.emit('error', {
              componentId,
              action: currentAction,
              error: result.error,
              message: result.error.message,
              timestamp: Date.now()
            }, componentId);

            // Also emit step.failed event
            this.eventBus.emit('step.failed', {
              componentId,
              action: currentAction,
              error: result.error,
              message: result.error.message,
              timestamp: Date.now()
            }, componentId);
          } else if (result.success) {
            // Emit step.completed event for successful execution
            this.eventBus.emit('step.completed', {
              componentId,
              action: currentAction,
              result: result.output,
              timestamp: Date.now()
            }, componentId);
          }

          // Add new actions to pending queue
          pendingActions.push(...result.nextActions);

          // Update context state with result data
          if (result.output !== undefined) {
            context.nodeOutputs.set(componentId, result.output);
          }

          // ✅ CRITICAL FIX: Merge sharedStateUpdates back into context.sharedState
          // This ensures that state updates from each agent are propagated to subsequent agents
          if (result.sharedStateUpdates && Object.keys(result.sharedStateUpdates).length > 0) {
            // Create a new sharedState object with merged updates
            const updatedSharedState = {
              ...context.sharedState,
              ...result.sharedStateUpdates
            };

            // Update the context with the new shared state
            // Note: We need to update the context object directly since it's readonly
            (context as any).sharedState = updatedSharedState;

            console.log(`🔄 PangeaFlow: Merged sharedStateUpdates from ${componentId}:`, {
              previousKeys: Object.keys(context.sharedState),
              newKeys: Object.keys(result.sharedStateUpdates),
              mergedKeys: Object.keys(updatedSharedState)
            });
          }

          // Merge events
          context.events.push(...result.events);
          
        } catch (error) {
          // Emit error event for monitoring
          this.eventBus.emit('error', {
            componentId,
            action: currentAction,
            error: error as Error,
            message: (error as Error).message,
            timestamp: Date.now()
          }, componentId);

          // Also emit step.failed event
          this.eventBus.emit('step.failed', {
            componentId,
            action: currentAction,
            error: error as Error,
            message: (error as Error).message,
            timestamp: Date.now()
          }, componentId);

          const errorResult: ExecutionResult = {
            success: false,
            error: error as Error,
            events: [],
            nextActions: ['error'],
            sharedStateUpdates: { componentId, action: currentAction },
          };
          results.push(errorResult);

          // Handle error routing
          if (this.routes.has('error')) {
            pendingActions.push('error');
          }
        }
      }
    }
    
    return results;
  }
  
  /**
   * Gets overall workflow execution statistics.
   * @returns Statistical summary of all component executions
   * @example
   * ```typescript
   * const stats = orchestrator.getMetrics();
   * console.log(`Success rate: ${stats.successRate * 100}%`);
   * console.log(`Average duration: ${stats.avgDuration}ms`);
   * ```
   */
  getMetrics(): ReturnType<TelemetryCollector['getStats']> {
    return this.telemetry.getStats();
  }

  /**
   * Registers an event listener for workflow events.
   * @param eventType - Type of event to listen for
   * @param handler - Function to call when event is emitted
   * @returns Unsubscribe function to remove the listener
   * @example
   * ```typescript
   * const unsubscribe = orchestrator.on('error', (event) => {
   *   console.error('Workflow error:', event.payload);
   * });
   *
   * // Later, cleanup
   * unsubscribe();
   * ```
   */
  on(eventType: string, handler: (event: WorkflowEvent) => void): () => void {
    return this.eventBus.on(eventType, handler);
  }
}

// ============================================================================
// BUILDER & FACTORY
// ============================================================================

/**
 * Builder for creating and configuring workflow orchestrators.
 * Provides a fluent API for adding agents and defining routes.
 * @example
 * ```typescript
 * const workflow = WorkflowBuilder
 *   .create()
 *   .addReasoningAgent('reasoning', async (prompt, context) => {
 *     return await callLLM(prompt, context);
 *   })
 *   .addToolAgent('tools', {
 *     'file_read': async (args) => await fs.readFile(args.path, 'utf8'),
 *     'api_call': async (args) => await fetch(args.url).then(r => r.json())
 *   })
 *   .addMemoryAgent('memory')
 *   .route('start', 'reasoning')
 *   .route('continue', 'tools', 'memory')
 *   .build();
 * ```
 */
class WorkflowBuilder {
  private readonly orchestrator = new WorkflowOrchestrator();

  /**
   * Creates a new workflow builder instance.
   * @returns New WorkflowBuilder instance
   */
  static create(): WorkflowBuilder {
    return new WorkflowBuilder();
  }



  /**
   * Adds a reasoning agent to the workflow.
   * @param id - Unique identifier for the agent
   * @param llmProvider - Function that calls the LLM with prompt and context
   * @returns This builder instance for method chaining
   * @example
   * ```typescript
   * builder.addReasoningAgent('reasoning', async (prompt, context) => {
   *   return await openai.chat.completions.create({
   *     model: 'gpt-4',
   *     messages: [{ role: 'user', content: prompt }]
   *   });
   * });
   * ```
   */
  addReasoningAgent(
    id: string,
    llmProvider: (prompt: string, context: Record<string, unknown>) => Promise<string>
  ): this {
    const agent = new ReasoningAgent(id, this.orchestrator['eventBus'], this.orchestrator['telemetry'], llmProvider);
    this.orchestrator.registerComponent(agent);
    return this;
  }

  /**
   * Adds a tool agent to the workflow.
   * @param id - Unique identifier for the agent
   * @param tools - Record of tool names to tool functions
   * @returns This builder instance for method chaining
   * @example
   * ```typescript
   * builder.addToolAgent('tools', {
   *   'file_read': async (args) => await fs.readFile(args.path, 'utf8'),
   *   'calculate': async (args) => args.a + args.b,
   *   'api_call': async (args) => await fetch(args.url).then(r => r.json())
   * });
   * ```
   */
  addToolAgent(id: string, tools: Record<string, (args: unknown) => Promise<unknown>>): this {
    const toolMap = new Map(Object.entries(tools));
    const agent = new ToolAgent(id, this.orchestrator['eventBus'], this.orchestrator['telemetry'], toolMap);
    this.orchestrator.registerComponent(agent);
    return this;
  }

  /**
   * Adds a memory agent to the workflow.
   * @param id - Unique identifier for the agent
   * @returns This builder instance for method chaining
   * @example
   * ```typescript
   * builder.addMemoryAgent('memory');
   * ```
   */
   addMemoryAgent(id: string, opts: MemoryAgentOptions = {}): this {
    const driverInstance: MemoryDriver =
      typeof opts.driver === 'string'
        ? memoryDrivers.create(opts.driver, opts.driverOptions)
        : opts.driver ?? memoryDrivers.create('mem');

    const agent = new MemoryAgent(
      id,
      this.orchestrator['eventBus'],
      this.orchestrator['telemetry'],
      driverInstance,
      opts.defaultTTL
    );
    this.orchestrator.registerComponent(agent);
    return this;
  }

  /**
   * Defines routing for an action to specific components.
   * @param action - Action name that triggers this route
   * @param componentIds - Component IDs to execute for this action
   * @returns This builder instance for method chaining
   * @example
   * ```typescript
   * builder
   *   .route('start', 'reasoning')
   *   .route('continue', 'tools', 'memory')
   *   .route('error', 'memory');
   * ```
   */
  route(action: string, ...componentIds: string[]): this {
    this.orchestrator.defineRoute(action, componentIds as ComponentId[]);
    return this;
  }

  /**
   * Builds and returns the configured workflow orchestrator.
   * @returns Configured WorkflowOrchestrator instance
   */
  build(): WorkflowOrchestrator {
    return this.orchestrator;
  }
}

// ============================================================================
// STREAMING & BATCH PROCESSING
// ============================================================================

/**
 * Streaming workflow processor for handling large datasets efficiently.
 * Processes items in batches to manage memory usage and provide incremental results.
 * @example
 * ```typescript
 * const streamingWorkflow = new StreamingWorkflow(orchestrator);
 *
 * // Process a stream of data
 * async function* dataStream() {
 *   for (let i = 0; i < 1000; i++) {
 *     yield { id: i, data: `item-${i}` };
 *   }
 * }
 *
 * // Process in batches of 50
 * for await (const results of streamingWorkflow.processStream(dataStream(), 'process_item', 50)) {
 *   console.log(`Processed batch of ${results.length} items`);
 * }
 * ```
 */
class StreamingWorkflow {
  /**
   * Creates a new streaming workflow processor.
   * @param orchestrator - Workflow orchestrator to use for processing
   */
  constructor(private readonly orchestrator: WorkflowOrchestrator) {}

  /**
   * Processes an async iterable stream in batches.
   * @template T - Type of items in the stream
   * @param items - Async iterable of items to process
   * @param startAction - Action to trigger for each batch
   * @param batchSize - Number of items to process in each batch (default: 10)
   * @yields Arrays of execution results for each processed batch
   * @example
   * ```typescript
   * async function* generateData() {
   *   for (let i = 0; i < 100; i++) {
   *     yield { id: i, value: Math.random() };
   *   }
   * }
   *
   * for await (const batchResults of streamingWorkflow.processStream(generateData(), 'analyze', 25)) {
   *   console.log(`Processed ${batchResults.length} items`);
   * }
   * ```
   */
  async *processStream<T>(
    items: AsyncIterable<T>,
    startAction: string,
    batchSize: number = 10
  ): AsyncGenerator<ExecutionResult[], void, unknown> {
    const batch: T[] = [];

    for await (const item of items) {
      batch.push(item);

      if (batch.length >= batchSize) {
        const results = await this.processBatch(batch, startAction);
        yield results;
        batch.length = 0;
      }
    }

    // Process remaining items
    if (batch.length > 0) {
      const results = await this.processBatch(batch, startAction);
      yield results;
    }
  }

  /**
   * Processes a batch of items concurrently.
   * @template T - Type of items in the batch
   * @param batch - Array of items to process
   * @param startAction - Action to trigger for each item
   * @returns Promise resolving to flattened array of execution results
   * @private
   */
  private async processBatch<T>(batch: T[], startAction: string): Promise<ExecutionResult[]> {
    const promises = batch.map((item, index) =>
      this.orchestrator.execute(startAction, {
        sharedState: { batchItem: item, batchIndex: index },
      })
    );

    const results = await Promise.all(promises);
    return results.flat();
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export {
  WorkflowBuilder,
  WorkflowOrchestrator,
  StreamingWorkflow,
  AgentComponent,
  ReasoningAgent,
  ToolAgent,
  MemoryAgent,
  EventBus,
  ReactiveState,
  TelemetryCollector,
};


export type {
  ComponentId,
  WorkflowEvent,
  ExecutionContext,
  ExecutionResult,
  TelemetryData,
};