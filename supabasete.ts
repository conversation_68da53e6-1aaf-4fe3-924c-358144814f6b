// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const SUPA_URL = process.env.SUPABASE_URL;
const SUPA_KEY = process.env.SUPABASE_ANON_KEY;


// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

const supabase = createClient(SUPA_URL, SUPA_KEY, { db: { schema: 'public' } });



  const {data, error } = await supabase
      .from("ddd")
      .upsert(
        { namespace: "this.ns", "key", "value" },
        { onConflict: 'namespace,key' }                    // :contentReference[oaicite:1]{index=1}
      );