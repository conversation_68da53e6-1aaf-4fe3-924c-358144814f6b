// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const SUPA_URL = process.env.SUPABASE_URL;
const SUPA_KEY = process.env.SUPABASE_ANON_KEY;


// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

if (!SUPA_URL || !SUPA_KEY) {
  throw new Error(
    '⚠️  Supabase tests require SUPABASE_URL & SUPABASE_ANON_KEY env vars'
  );
}

const supabase = createClient(SUPA_URL, SUPA_KEY, { db: { schema: 'public' } });

// Async function to handle the database operation
async function testSupabaseConnection() {
  const {data, error } = await supabase
      .from("pangeaflow_memory")
      .upsert(
        { namespace: "this.ns", key: "foo", value: "bar" },
        { onConflict: 'namespace,key' }
      );

  if (error) {
    console.error('Supabase error:', error);
  } else {
    console.log('Supabase operation successful:', data);
  }

  return { data, error };
}
const result = await testSupabaseConnection();
// Export the function and supabase client
export { supabase, testSupabaseConnection };